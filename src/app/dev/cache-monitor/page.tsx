'use client'

/**
 * 开发环境缓存监控页面
 * 仅在开发环境下可用
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/data-display/card'
import { Button } from '@/components/ui/forms/button'
import { RefreshCw, Trash2, BarChart3 } from 'lucide-react'

interface CacheStats {
  timestamp: string
  global: {
    hits: number
    misses: number
    errors: number
    hitRate: string
    total: number
  }
  functions: Record<string, {
    hits: number
    misses: number
    errors: number
    total: number
    hitRate: string
    lastAccess: string
  }>
  analysis: {
    summary: {
      totalFunctions: number
      highPerformanceFunctions: number
      lowPerformanceFunctions: number
      averageHitRate: string
    }
    recommendations: string[]
  }
}

export default function CacheMonitorPage() {
  const [stats, setStats] = useState<CacheStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [autoRefresh, setAutoRefresh] = useState(false)

  // 获取缓存统计
  const fetchStats = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/dev/cache-stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data.data)
      } else {
        console.error('Failed to fetch cache stats')
      }
    } catch (error) {
      console.error('Error fetching cache stats:', error)
    } finally {
      setLoading(false)
    }
  }

  // 重置统计
  const resetStats = async () => {
    try {
      const response = await fetch('/api/dev/cache-stats', { method: 'DELETE' })
      if (response.ok) {
        await fetchStats()
      }
    } catch (error) {
      console.error('Error resetting cache stats:', error)
    }
  }

  // 触发性能分析
  const analyzePerformance = async () => {
    try {
      const response = await fetch('/api/dev/cache-stats', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'analyze' })
      })
      if (response.ok) {
        // eslint-disable-next-line no-console
        console.log('Performance analysis triggered - check console')
      }
    } catch (error) {
      console.error('Error triggering analysis:', error)
    }
  }

  // 自动刷新
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchStats, 5000) // 每5秒刷新
      return () => clearInterval(interval)
    }
    return undefined
  }, [autoRefresh])

  // 初始加载
  useEffect(() => {
    fetchStats()
  }, [])

  // 获取性能等级颜色
  const getPerformanceColor = (hitRate: string) => {
    const rate = parseFloat(hitRate)
    if (rate >= 80) return 'bg-green-500 text-white'
    if (rate >= 60) return 'bg-yellow-500 text-black'
    return 'bg-red-500 text-white'
  }

  // 获取性能等级文本
  const getPerformanceText = (hitRate: string) => {
    const rate = parseFloat(hitRate)
    if (rate >= 80) return '优秀'
    if (rate >= 60) return '良好'
    return '需优化'
  }

  if (process.env.NODE_ENV !== 'development') {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-muted-foreground">
              此页面仅在开发环境下可用
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">缓存性能监控</h1>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? '停止自动刷新' : '开启自动刷新'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchStats}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={resetStats}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            重置
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={analyzePerformance}
          >
            <BarChart3 className="h-4 w-4 mr-2" />
            分析
          </Button>
        </div>
      </div>

      {stats && (
        <>
          {/* 全局统计 */}
          <Card>
            <CardHeader>
              <CardTitle>全局缓存统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{stats.global.hits}</div>
                  <div className="text-sm text-muted-foreground">命中次数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{stats.global.misses}</div>
                  <div className="text-sm text-muted-foreground">未命中次数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{stats.global.errors}</div>
                  <div className="text-sm text-muted-foreground">错误次数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{stats.global.total}</div>
                  <div className="text-sm text-muted-foreground">总调用次数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{stats.global.hitRate}</div>
                  <div className="text-sm text-muted-foreground">命中率</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 性能摘要 */}
          <Card>
            <CardHeader>
              <CardTitle>性能摘要</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-xl font-bold">{stats.analysis.summary.totalFunctions}</div>
                  <div className="text-sm text-muted-foreground">缓存函数总数</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-green-600">{stats.analysis.summary.highPerformanceFunctions}</div>
                  <div className="text-sm text-muted-foreground">高性能函数</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-red-600">{stats.analysis.summary.lowPerformanceFunctions}</div>
                  <div className="text-sm text-muted-foreground">低性能函数</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold">{stats.analysis.summary.averageHitRate}</div>
                  <div className="text-sm text-muted-foreground">平均命中率</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 函数级统计 */}
          <Card>
            <CardHeader>
              <CardTitle>函数级缓存统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(stats.functions)
                  .sort(([, a], [, b]) => b.total - a.total)
                  .map(([functionName, functionStats]) => (
                    <div key={functionName} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold ${getPerformanceColor(functionStats.hitRate)}`}>
                          {getPerformanceText(functionStats.hitRate)}
                        </span>
                        <span className="font-medium">{functionName}</span>
                      </div>
                      <div className="flex items-center gap-6 text-sm">
                        <span>命中率: <strong>{functionStats.hitRate}</strong></span>
                        <span>命中: <strong className="text-green-600">{functionStats.hits}</strong></span>
                        <span>未命中: <strong className="text-red-600">{functionStats.misses}</strong></span>
                        <span>总计: <strong>{functionStats.total}</strong></span>
                        <span className="text-muted-foreground">
                          最后访问: {new Date(functionStats.lastAccess).toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>

          {/* 优化建议 */}
          <Card>
            <CardHeader>
              <CardTitle>优化建议</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {stats.analysis.recommendations.map((recommendation, index) => (
                  <div key={index} className="p-3 bg-blue-50 border-l-4 border-blue-400 rounded">
                    <p className="text-sm text-black">{recommendation}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {!stats && !loading && (
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-muted-foreground">
              暂无缓存统计数据，请先调用一些 API
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
